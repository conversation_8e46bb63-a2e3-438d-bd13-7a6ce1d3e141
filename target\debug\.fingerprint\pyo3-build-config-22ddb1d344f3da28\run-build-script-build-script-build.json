{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9193811874639612694, "build_script_build", false, 2040480598993624551]], "local": [{"RerunIfEnvChanged": {"var": "PYO3_CONFIG_FILE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_NO_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "VIRTUAL_ENV", "val": null}}, {"RerunIfEnvChanged": {"var": "CONDA_PREFIX", "val": null}}, {"RerunIfEnvChanged": {"var": "PATH", "val": "C:\\Program Files\\Microsoft MPI\\Bin\\;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\libnvvp;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\MATLAB\\R2024a\\bin;C:\\Program Files\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.3.0\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files (x86)\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\150\\DTS\\Binn\\;C:\\Program Files (x86)\\Windows Kits\\8.1\\Windows Performance Toolkit\\;C:\\Program Files\\Wolfram Research\\WolframScript\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\RedHat\\Podman\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\bin;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C:\\Users\\<USER>\\scoop\\apps\\llvm\\current\\bin;C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\bin;C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\;C:\\Users\\<USER>\\scoop\\apps\\mingw\\current\\bin;C:\\Users\\<USER>\\scoop\\shims;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64;C:\\Users\\<USER>\\vcpkg;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\Hostx64\\x64;C:\\Users\\<USER>\\Downloads\\wasi-sdk\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.lmstudio\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts;C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\bin"}}], "rustflags": [], "config": 0, "compile_kind": 0}